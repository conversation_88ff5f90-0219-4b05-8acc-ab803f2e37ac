import gleam/list
import gleam/int
import lustre
import lustre/element.{type Element}
import lustre/element/html
import lustre/effect.{type Effect}

import button

// TYPES -----------------------------------------------------------------------

pub type Model {
  Model(count: Int, loading: Bool)
}

pub type Msg {
  // Local actions
  Increment
  Decrement
  Reset
}



// INIT ------------------------------------------------------------------------

fn init(_flags) -> #(Model, Effect(Msg)) {
  #(Model(count: 0, loading: False), effect.none())
}

// UPDATE ----------------------------------------------------------------------

fn update(model: Model, msg: Msg) -> #(Model, Effect(Msg)) {
  case msg {
    // Local actions - update counter directly
    Increment -> #(Model(count: model.count + 1, loading: False), effect.none())
    Decrement -> #(Model(count: model.count - 1, loading: False), effect.none())
    Reset -> #(Model(count: 0, loading: False), effect.none())
  }
}

// VIEW ------------------------------------------------------------------------


fn render_elements(model: Model) -> List(Element(Msg)) {
  list.map(
    list.range(0, model.count),
    fn(el) { html.li([], [element.text(int.to_string(el))]) }
  )
}



fn buttons(model: Model) -> Element(Msg) {
let count = model.count |> int.to_string

  html.div([], [
    html.h1([], [element.text("Riva Gleam Counter")]),
    html.p([], [element.text("Count: " <> count)]),
    case model.loading {
      True -> html.p([], [element.text("Loading...")])
      False -> html.div([], [])
    },
    button.primary("+", Increment),
    button.secondary("-", Decrement),
    button.danger("Reset", Reset)
  ])
}




fn view(model: Model) -> Element(Msg) {
  html.div([], [
    buttons(model),
    html.ul([], render_elements(model))
  ])
}

// MAIN ------------------------------------------------------------------------

pub fn main() -> Nil {
  let app = lustre.application(init, update, view)
  let assert Ok(_) = lustre.start(app, "#app", Nil)
  Nil
}
